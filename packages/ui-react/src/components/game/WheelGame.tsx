import {
  CASINO_GAME_TYPE,
  FORMAT_TYPE,
  formatRawAmount,
  Wheel,
  WeightedGameConfiguration,
} from "@betswirl/sdk-core"
import { useMemo } from "react"
import wheelBackground from "../../assets/game/game-background.jpg"
import { useGameLogic } from "../../hooks/useGameLogic"
import { useWeightedGameConfiguration } from "../../hooks/useWeightedGameConfiguration"
import { GameDefinition } from "../../types/types"
import { GameFrame } from "./GameFrame"
import { GameConnectWallet } from "./shared/GameConnectWallet"
import { BaseGameProps } from "./shared/types"
import { useGameControls } from "./shared/useGameControls"
import { WheelGameControls } from "./WheelGameControls"

const wheelGameDefinition: GameDefinition<{ game: CASINO_GAME_TYPE.WHEEL; choice: number }> = {
  gameType: CASINO_GAME_TYPE.WHEEL,
  defaultSelection: {
    game: CASINO_GAME_TYPE.WHEEL,
    choice: 0,
  },
  getMultiplier: () => 10000, // Default multiplier, will be overridden by actual game logic
  encodeInput: (choice) => Wheel.encodeInput({ configId: choice }),
  getWinChancePercent: () => 10, // Default win chance, will be calculated based on config
}

export interface WheelGameProps extends BaseGameProps {}

export function WheelGame({
  theme = "system",
  customTheme,
  backgroundImage = wheelBackground,
  ...props
}: WheelGameProps) {
  const {
    isWalletConnected,
    balance,
    token,
    areChainsSynced,
    gameHistory,
    refreshHistory,
    betAmount,
    selection,
    setSelection,
    betStatus,
    gameResult,
    vrfFees,
    formattedVrfFees,
    gasPrice,
    targetPayoutAmount,
    formattedNetMultiplier,
    grossMultiplier,
    houseEdge,
    isInGameResultState,
    isGamePaused,
    nativeCurrencySymbol,
    themeSettings: baseThemeSettings,
    handlePlayButtonClick,
    handleBetAmountChange,
    needsTokenApproval,
    isApprovePending,
    isApproveConfirming,
    isRefetchingAllowance,
    approveError,
  } = useGameLogic({
    gameDefinition: wheelGameDefinition,
    backgroundImage,
  })

  const { config: wheelConfig } = useWeightedGameConfiguration({
    game: CASINO_GAME_TYPE.WHEEL,
    token,
  })

  const themeSettings = { ...baseThemeSettings, theme, customTheme }
  const isControlsDisabled = useGameControls(
    isWalletConnected,
    betStatus,
    isInGameResultState,
    isGamePaused,
  )

  const winningMultiplier = useMemo(() => {
    if (!gameResult?.rolled || !wheelConfig) return undefined
    const rolled = gameResult.rolled as { multiplier: number }
    return rolled.multiplier
  }, [gameResult, wheelConfig])

  if (!wheelConfig) {
    return (
      <GameFrame themeSettings={themeSettings} {...props}>
        <GameFrame.Header title="Wheel" connectWalletButton={<GameConnectWallet />} />
        <GameFrame.GameArea>
          <div className="flex items-center justify-center h-full">
            <div className="text-center">Loading wheel configuration...</div>
          </div>
        </GameFrame.GameArea>
      </GameFrame>
    )
  }

  return (
    <GameFrame themeSettings={themeSettings} {...props}>
      <GameFrame.Header title="Wheel" connectWalletButton={<GameConnectWallet />} />
      <GameFrame.GameArea>
        <GameFrame.InfoButton
          winChance={10} // TODO: Calculate actual win chance based on selected multiplier
          rngFee={formattedVrfFees}
          targetPayout={formatRawAmount(targetPayoutAmount, token.decimals, FORMAT_TYPE.PRECISE)}
          gasPrice={gasPrice}
          tokenDecimals={token.decimals}
          nativeCurrencySymbol={nativeCurrencySymbol}
        />
        <GameFrame.HistoryButton historyData={gameHistory} onHistoryOpen={refreshHistory} />
        <GameFrame.GameControls>
          <WheelGameControls
            config={wheelConfig}
            winningMultiplier={winningMultiplier}
            multiplier={formattedNetMultiplier}
            isDisabled={isControlsDisabled}
            theme={themeSettings.theme}
            betAmount={betAmount}
            token={token}
            houseEdge={houseEdge}
          />
        </GameFrame.GameControls>
        <GameFrame.ResultWindow gameResult={gameResult} currency={token.symbol} />
      </GameFrame.GameArea>
      <GameFrame.BettingSection
        game={CASINO_GAME_TYPE.WHEEL}
        betCount={1}
        grossMultiplier={grossMultiplier}
        balance={balance}
        isConnected={isWalletConnected}
        token={token}
        betStatus={betStatus}
        betAmount={betAmount}
        vrfFees={vrfFees}
        onBetAmountChange={handleBetAmountChange}
        onPlayBtnClick={handlePlayButtonClick}
        areChainsSynced={areChainsSynced}
        isGamePaused={isGamePaused}
        hasValidSelection={true} // Wheel game always has valid selection
        needsTokenApproval={needsTokenApproval}
        isApprovePending={isApprovePending}
        isApproveConfirming={isApproveConfirming}
        isRefetchingAllowance={isRefetchingAllowance}
        approveError={approveError}
      />
    </GameFrame>
  )
}
