import {
  CASINO_GAME_TYPE,
  getWeightedGameConfigurationFunctionData,
  parseRawWeightedGameConfiguration,
  WeightedGameConfiguration,
  wheelCachedConfigurations,
  type RawWeightedGameConfiguration,
} from "@betswirl/sdk-core"
import { useMemo } from "react"
import { useReadContract } from "wagmi"
import { use<PERSON>hain } from "../context/chainContext"
import { TokenWithImage } from "../types/types"

type UseWeightedGameConfigurationProps = {
  game: CASINO_GAME_TYPE.WHEEL | CASINO_GAME_TYPE.PLINKO
  token: TokenWithImage
  configId?: number
  query?: {
    enabled?: boolean
    refetchInterval?: number
    staleTime?: number
    refetchOnWindowFocus?: boolean
  }
}

type UseWeightedGameConfigurationResult = {
  wagmiHook: ReturnType<typeof useReadContract>
  config: WeightedGameConfiguration | undefined
  loading: boolean
  error: Error | null
}

/**
 * Fetches weighted game configuration from the smart contract or cached configurations.
 * Configuration includes weights, multipliers, colors, and other game parameters.
 *
 * @param props.game - Type of weighted game (WHEEL or PLINKO)
 * @param props.token - Token for which to fetch configuration
 * @param props.configId - Configuration ID to fetch (defaults to 0)
 * @param props.query - Optional query settings (refetchInterval, enabled, etc.)
 * @returns Weighted game configuration with loading and error states
 * @returns config - Weighted game configuration object with game parameters
 * @returns loading - Whether the configuration is currently being fetched
 * @returns error - Error object if configuration fetch failed
 *
 * @example
 * ```ts
 * const { config, loading, error } = useWeightedGameConfiguration({
 *   game: CASINO_GAME_TYPE.WHEEL,
 *   token: ethToken,
 *   configId: 0
 * })
 * if (config) {
 *   console.log('Weights:', config.weights)
 *   console.log('Multipliers:', config.multipliers)
 *   console.log('Colors:', config.colors)
 * }
 * ```
 */
export function useWeightedGameConfiguration(
  props: UseWeightedGameConfigurationProps,
): UseWeightedGameConfigurationResult {
  const { appChainId } = useChain()
  const { game, token, configId = 0, query = {} } = props

  // Check if we have cached configuration first
  const cachedConfig = useMemo(() => {
    if (game === CASINO_GAME_TYPE.WHEEL) {
      const cachedConfigs = wheelCachedConfigurations[appChainId]
      return cachedConfigs?.find((config) => config.configId === configId)
    }
    // Add other games here when needed (PLINKO, etc.)
    return undefined
  }, [game, appChainId, configId])

  const functionData = useMemo(() => {
    if (cachedConfig) return null
    return getWeightedGameConfigurationFunctionData(configId, appChainId)
  }, [configId, appChainId, cachedConfig])

  const wagmiHook = useReadContract({
    abi: functionData?.data.abi,
    address: functionData?.data.to,
    functionName: functionData?.data.functionName,
    args: functionData?.data.args,
    chainId: appChainId,
    query: {
      enabled: !cachedConfig && (query?.enabled ?? true),
      staleTime: query?.staleTime ?? 5 * 60 * 1000, // 5 minutes - configuration rarely changes
      refetchOnWindowFocus: query?.refetchOnWindowFocus ?? false,
      refetchInterval: query?.refetchInterval,
    },
  })

  const config = useMemo(() => {
    // Return cached config if available
    if (cachedConfig) {
      return cachedConfig
    }

    // Parse contract data if available
    if (!wagmiHook.data) return undefined
    return parseRawWeightedGameConfiguration(
      wagmiHook.data as RawWeightedGameConfiguration,
      configId,
      appChainId,
    )
  }, [cachedConfig, wagmiHook.data, configId, appChainId])

  return {
    wagmiHook,
    config,
    loading: !cachedConfig && (wagmiHook.isLoading || wagmiHook.isFetching),
    error: wagmiHook.error as Error | null,
  }
}
